# F2FS Wear Leveling Enhancement

This project synchronizes advanced wear leveling and garbage collection improvements from bbssd (FEMU-based SSD simulator) to the F2FS (Flash-Friendly File System).

## Overview

The bbssd implementation includes sophisticated wear leveling algorithms and garbage collection strategies that significantly improve SSD endurance and performance. This project ports these key improvements to F2FS to achieve similar benefits in a production filesystem.

## Key Features Implemented

### 1. CV (Cold/Voltage) Block Allocation Policy
- **Source**: bbssd's CV allocation strategy (bbssd/ftl.c lines 225-270)
- **Implementation**: Enhanced `get_new_segment()` in f2fs/segment.c
- **Benefits**: Prioritizes blocks with higher erase counts for allocation, improving wear distribution

### 2. Read-Heavy Data Detection and Migration
- **Source**: bbssd's read count tracking and migration (bbssd/ftl.c lines 663-760)
- **Implementation**: Added read count tracking in f2fs/data.c and migration logic in f2fs/segment.c
- **Benefits**: Proactively migrates frequently read data from aged blocks

### 3. Enhanced Victim Selection Algorithm
- **Source**: bbssd's multi-factor victim selection (bbssd/ftl.c lines 1117-1200)
- **Implementation**: Modified `get_gc_cost()` in f2fs/gc.c
- **Benefits**: Considers both valid page counts and erase counts for better GC decisions

### 4. Progressive Wear Leveling
- **Source**: bbssd's PWL algorithm (bbssd/ftl.c lines 1388-1450)
- **Implementation**: Added wear leveling management in f2fs/segment.c
- **Benefits**: Threshold-based wear leveling with configurable parameters

### 5. Comprehensive Statistics and Control
- **Source**: bbssd's control interface (bbssd/bb.c)
- **Implementation**: Added sysfs interface in f2fs/sysfs.c
- **Benefits**: Runtime monitoring and configuration of wear leveling parameters

## File Modifications

### Core Data Structures
- **f2fs/segment.h**: Added `erase_count`, `read_count`, `last_read_time` to `seg_entry`
- **f2fs/f2fs.h**: Added `wear_leveling` management structure to `f2fs_sb_info`

### Algorithm Implementation
- **f2fs/segment.c**: 
  - CV block allocation policy
  - Wear leveling helper functions
  - Progressive wear leveling algorithm
  - Erase count tracking
- **f2fs/gc.c**: Enhanced victim selection considering erase counts
- **f2fs/data.c**: Read count tracking in I/O completion

### User Interface
- **f2fs/sysfs.c**: Added wear leveling control and monitoring interface

## Sysfs Interface

The following sysfs attributes are available under `/sys/fs/f2fs/<device>/`:

- `wear_leveling_enable`: Enable/disable wear leveling (0/1)
- `cv_moderate`: Control CV allocation mode (0=normal, 1=moderate)
- `wear_leveling_stats`: View current erase count statistics
- `read_heavy_threshold`: Configure read-heavy detection threshold

## Building and Testing

### Prerequisites
- Linux kernel source with F2FS support
- GCC compiler for test program
- F2FS mounted filesystem for testing

### Build Test Program
```bash
make all
```

### Run Tests
```bash
./test_wear_leveling <f2fs_device> <test_directory>
```

Example:
```bash
./test_wear_leveling sda1 /mnt/f2fs/test
```

### View Summary
```bash
make summary
```

## Technical Details

### CV Block Allocation Algorithm
The CV (Cold/Voltage) policy prioritizes blocks based on erase counts:
- **Normal mode**: Prefers blocks with highest erase counts
- **Moderate mode**: Prefers blocks with lowest erase counts
- Integrated into F2FS segment allocation logic

### Read-Heavy Data Detection
- Tracks read operations per segment
- Identifies segments with read counts above threshold
- Triggers proactive migration for aged, frequently-read data

### Enhanced Victim Selection
The garbage collection cost function now considers:
- Valid block count (original F2FS behavior)
- Erase count relative to average
- Read-heavy segment status
- Configurable cost reduction factors

### Wear Leveling Management
- Tracks total and average erase counts
- Monitors wear distribution across segments
- Provides configurable thresholds and parameters
- Supports runtime enable/disable

## Performance Benefits

Based on bbssd's demonstrated improvements:
- **Improved Endurance**: Better wear distribution extends SSD lifespan
- **Reduced Write Amplification**: Smarter victim selection reduces unnecessary data movement
- **Better Performance**: Read-heavy data migration improves access patterns
- **Configurable Behavior**: Runtime tuning for different workloads

## Compatibility

- Compatible with existing F2FS features
- Backward compatible with existing F2FS filesystems
- Optional wear leveling can be disabled if needed
- No changes to on-disk format

## Future Enhancements

Potential areas for further improvement:
- Integration with F2FS's existing ATGC (Age Threshold Garbage Collection)
- Support for multi-stream SSDs
- Machine learning-based wear prediction
- Integration with filesystem-level wear leveling

## References

- bbssd: Black-box SSD implementation in FEMU
- F2FS: Flash-Friendly File System
- FEMU: Flash Memory Emulator framework

## License

This enhancement follows the same licensing as the original F2FS implementation (GPL-2.0).
