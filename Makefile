# Makefile for F2FS wear leveling test and kernel modules

# Kernel build directory
KDIR ?= /lib/modules/$(shell uname -r)/build

# Test program
TEST_PROG = test_wear_leveling
TEST_SRC = test_wear_leveling.c

# F2FS source files (for reference)
F2FS_SRCS = f2fs/segment.c f2fs/gc.c f2fs/data.c f2fs/sysfs.c f2fs/f2fs.h f2fs/segment.h f2fs/gc.h

.PHONY: all clean test help

all: $(TEST_PROG)

# Build test program
$(TEST_PROG): $(TEST_SRC)
	gcc -Wall -Wextra -O2 -o $@ $<

# Clean build artifacts
clean:
	rm -f $(TEST_PROG)
	rm -f *.o

# Run basic test (requires F2FS mount)
test: $(TEST_PROG)
	@echo "To run the test, use:"
	@echo "./$(TEST_PROG) <f2fs_device> <test_directory>"
	@echo ""
	@echo "Example:"
	@echo "./$(TEST_PROG) sda1 /mnt/f2fs/test"
	@echo ""
	@echo "Make sure:"
	@echo "1. F2FS filesystem is mounted"
	@echo "2. Test directory exists and is writable"
	@echo "3. You have permission to access sysfs"

# Show help
help:
	@echo "F2FS Wear Leveling Enhancement Makefile"
	@echo ""
	@echo "Targets:"
	@echo "  all     - Build test program"
	@echo "  clean   - Remove build artifacts"
	@echo "  test    - Show test instructions"
	@echo "  help    - Show this help"
	@echo ""
	@echo "Files modified for wear leveling:"
	@echo "  f2fs/segment.h  - Added erase_count, read_count to seg_entry"
	@echo "  f2fs/f2fs.h     - Added wear_leveling management structure"
	@echo "  f2fs/segment.c  - Implemented CV allocation, wear leveling functions"
	@echo "  f2fs/gc.c       - Enhanced victim selection with erase count"
	@echo "  f2fs/data.c     - Added read count tracking"
	@echo "  f2fs/sysfs.c    - Added wear leveling sysfs interface"
	@echo ""
	@echo "Key features implemented:"
	@echo "  - CV (Cold/Voltage) block allocation policy"
	@echo "  - Read-heavy data detection and migration"
	@echo "  - Enhanced victim selection considering erase counts"
	@echo "  - Wear leveling statistics and control via sysfs"
	@echo "  - Progressive wear leveling algorithm"

# Show summary of changes
summary:
	@echo "=== F2FS Wear Leveling Enhancement Summary ==="
	@echo ""
	@echo "Based on bbssd improvements, the following features were added to F2FS:"
	@echo ""
	@echo "1. Data Structure Enhancements:"
	@echo "   - Added erase_count, read_count, last_read_time to seg_entry"
	@echo "   - Added wear_leveling management structure to f2fs_sb_info"
	@echo ""
	@echo "2. CV Block Allocation Policy:"
	@echo "   - Prioritizes blocks with higher erase counts for allocation"
	@echo "   - Supports both normal and moderate CV modes"
	@echo "   - Integrated into get_new_segment() function"
	@echo ""
	@echo "3. Read-Heavy Data Detection:"
	@echo "   - Tracks read counts per segment"
	@echo "   - Detects frequently read data from aged blocks"
	@echo "   - Proactive migration of read-heavy data"
	@echo ""
	@echo "4. Enhanced Victim Selection:"
	@echo "   - Modified get_gc_cost() to consider erase counts"
	@echo "   - Prefers segments with higher erase counts for GC"
	@echo "   - Reduces cost for read-heavy segments"
	@echo ""
	@echo "5. Wear Leveling Management:"
	@echo "   - Progressive wear leveling algorithm"
	@echo "   - Configurable thresholds and parameters"
	@echo "   - Statistics tracking and reporting"
	@echo ""
	@echo "6. Sysfs Interface:"
	@echo "   - wear_leveling_enable: Enable/disable wear leveling"
	@echo "   - cv_moderate: Control CV allocation mode"
	@echo "   - wear_leveling_stats: View erase count statistics"
	@echo "   - read_heavy_threshold: Configure read-heavy detection"
	@echo ""
	@echo "These enhancements bring bbssd's advanced wear leveling and"
	@echo "garbage collection improvements to the F2FS filesystem."
