# SPDX-License-Identifier: GPL-2.0-only
config F2FS_FS
	tristate "F2FS filesystem support"
	depends on BLOCK
	select NLS
	select CRYP<PERSON>
	select CRYPTO_CRC32
	select F2FS_FS_XATTR if FS_ENCRYPTION
	select FS_ENCRYPTION_ALGS if FS_ENCRYPTION
	select LZ4_COMPRESS if F2FS_FS_LZ4
	select LZ4_DECOMPRESS if F2FS_FS_LZ4
	select LZ4HC_COMPRESS if F2FS_FS_LZ4HC
	select LZO_COMPRESS if F2FS_FS_LZO
	select LZO_DECOMPRESS if F2FS_FS_LZO
	select ZSTD_COMPRESS if F2FS_FS_ZSTD
	select ZSTD_DECOMPRESS if F2FS_FS_ZSTD
	help
	  F2FS is based on Log-structured File System (LFS), which supports
	  versatile "flash-friendly" features. The design has been focused on
	  addressing the fundamental issues in LFS, which are snowball effect
	  of wandering tree and high cleaning overhead.

	  Since flash-based storages show different characteristics according to
	  the internal geometry or flash memory management schemes aka FTL, F2FS
	  and tools support various parameters not only for configuring on-disk
	  layout, but also for selecting allocation and cleaning algorithms.

	  If unsure, say N.

config F2FS_STAT_FS
	bool "F2FS Status Information"
	depends on F2FS_FS
	default y
	help
	  /sys/kernel/debug/f2fs/ contains information about all the partitions
	  mounted as f2fs. Each file shows the whole f2fs information.

	  /sys/kernel/debug/f2fs/status includes:
	    - major filesystem information managed by f2fs currently
	    - average SIT information about whole segments
	    - current memory footprint consumed by f2fs.

config F2FS_FS_XATTR
	bool "F2FS extended attributes"
	depends on F2FS_FS
	default y
	help
	  Extended attributes are name:value pairs associated with inodes by
	  the kernel or by users (see the attr(5) manual page for details).

	  If unsure, say N.

config F2FS_FS_POSIX_ACL
	bool "F2FS Access Control Lists"
	depends on F2FS_FS_XATTR
	select FS_POSIX_ACL
	default y
	help
	  Posix Access Control Lists (ACLs) support permissions for users and
	  groups beyond the owner/group/world scheme.

	  If you don't know what Access Control Lists are, say N

config F2FS_FS_SECURITY
	bool "F2FS Security Labels"
	depends on F2FS_FS_XATTR
	help
	  Security labels provide an access control facility to support Linux
	  Security Models (LSMs) accepted by AppArmor, SELinux, Smack and TOMOYO
	  Linux. This option enables an extended attribute handler for file
	  security labels in the f2fs filesystem, so that it requires enabling
	  the extended attribute support in advance. In particular you need this
	  option if you use the setcap command to assign initial process capabi-
	  lities to executables (the security.* extended attributes).

	  If you are not using a security module, say N.

config F2FS_CHECK_FS
	bool "F2FS consistency checking feature"
	depends on F2FS_FS
	help
	  Enables BUG_ONs which check the filesystem consistency in runtime.

	  If you want to improve the performance, say N.

config F2FS_FAULT_INJECTION
	bool "F2FS fault injection facility"
	depends on F2FS_FS
	help
	  Test F2FS to inject faults such as ENOMEM, ENOSPC, and so on.

	  If unsure, say N.

config F2FS_FS_COMPRESSION
	bool "F2FS compression feature"
	depends on F2FS_FS
	help
	  Enable filesystem-level compression on f2fs regular files,
	  multiple back-end compression algorithms are supported.

config F2FS_FS_LZO
	bool "LZO compression support"
	depends on F2FS_FS_COMPRESSION
	default y
	help
	  Support LZO compress algorithm, if unsure, say Y.

config F2FS_FS_LZORLE
	bool "LZO-RLE compression support"
	depends on F2FS_FS_LZO
	default y
	help
	  Support LZO-RLE compress algorithm, if unsure, say Y.

config F2FS_FS_LZ4
	bool "LZ4 compression support"
	depends on F2FS_FS_COMPRESSION
	default y
	help
	  Support LZ4 compress algorithm, if unsure, say Y.

config F2FS_FS_LZ4HC
	bool "LZ4HC compression support"
	depends on F2FS_FS_LZ4
	default y
	help
	  Support LZ4HC compress algorithm, LZ4HC has compatible on-disk
	  layout with LZ4, if unsure, say Y.

config F2FS_FS_ZSTD
	bool "ZSTD compression support"
	depends on F2FS_FS_COMPRESSION
	default y
	help
	  Support ZSTD compress algorithm, if unsure, say Y.

config F2FS_IOSTAT
	bool "F2FS IO statistics information"
	depends on F2FS_FS
	default y
	help
	  Support getting IO statistics through sysfs and printing out periodic
	  IO statistics tracepoint events. You have to turn on "iostat_enable"
	  sysfs node to enable this feature.
