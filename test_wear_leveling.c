/*
 * Test program for F2FS wear leveling enhancements
 * Based on bbssd improvements
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <errno.h>

/* For sync() function */
#ifdef __linux__
#include <unistd.h>
#else
/* Fallback for non-Linux systems */
#define sync() do { } while(0)
#endif

#define SYSFS_PATH "/sys/fs/f2fs"
#define TEST_FILE_SIZE (1024 * 1024)  /* 1MB */
#define NUM_TEST_FILES 10

/* Test wear leveling sysfs interface */
int test_sysfs_interface(const char *mount_point) {
    char path[256];
    char buffer[256];
    FILE *fp;
    int ret = 0;
    
    printf("Testing F2FS wear leveling sysfs interface...\n");
    
    /* Test wear_leveling_enable */
    snprintf(path, sizeof(path), "%s/%s/wear_leveling_enable", SYSFS_PATH, mount_point);
    fp = fopen(path, "r");
    if (fp) {
        if (fgets(buffer, sizeof(buffer), fp)) {
            printf("Wear leveling enabled: %s", buffer);
        }
        fclose(fp);
    } else {
        printf("Warning: Cannot read %s\n", path);
        ret = -1;
    }
    
    /* Test cv_moderate */
    snprintf(path, sizeof(path), "%s/%s/cv_moderate", SYSFS_PATH, mount_point);
    fp = fopen(path, "r");
    if (fp) {
        if (fgets(buffer, sizeof(buffer), fp)) {
            printf("CV moderate mode: %s", buffer);
        }
        fclose(fp);
    } else {
        printf("Warning: Cannot read %s\n", path);
        ret = -1;
    }
    
    /* Test wear_leveling_stats */
    snprintf(path, sizeof(path), "%s/%s/wear_leveling_stats", SYSFS_PATH, mount_point);
    fp = fopen(path, "r");
    if (fp) {
        printf("Wear leveling statistics:\n");
        while (fgets(buffer, sizeof(buffer), fp)) {
            printf("  %s", buffer);
        }
        fclose(fp);
    } else {
        printf("Warning: Cannot read %s\n", path);
        ret = -1;
    }
    
    /* Test read_heavy_threshold */
    snprintf(path, sizeof(path), "%s/%s/read_heavy_threshold", SYSFS_PATH, mount_point);
    fp = fopen(path, "r");
    if (fp) {
        if (fgets(buffer, sizeof(buffer), fp)) {
            printf("Read heavy threshold: %s", buffer);
        }
        fclose(fp);
    } else {
        printf("Warning: Cannot read %s\n", path);
        ret = -1;
    }
    
    return ret;
}

/* Test CV block allocation by creating and deleting files */
int test_cv_allocation(const char *test_dir) {
    char filename[256];
    char *data;
    int fd, i;
    int ret = 0;
    
    printf("Testing CV block allocation...\n");
    
    data = malloc(TEST_FILE_SIZE);
    if (!data) {
        printf("Error: Cannot allocate test data\n");
        return -1;
    }
    
    /* Fill with test pattern */
    memset(data, 0xAA, TEST_FILE_SIZE);
    
    /* Create test files to trigger block allocation */
    for (i = 0; i < NUM_TEST_FILES; i++) {
        snprintf(filename, sizeof(filename), "%s/test_file_%d", test_dir, i);
        
        fd = open(filename, O_CREAT | O_WRONLY | O_TRUNC, 0644);
        if (fd < 0) {
            printf("Error: Cannot create %s: %s\n", filename, strerror(errno));
            ret = -1;
            break;
        }
        
        if (write(fd, data, TEST_FILE_SIZE) != TEST_FILE_SIZE) {
            printf("Error: Cannot write to %s: %s\n", filename, strerror(errno));
            close(fd);
            ret = -1;
            break;
        }
        
        close(fd);
        printf("Created test file %d\n", i);
    }
    
    /* Sync to ensure data is written */
    sync();
    
    /* Clean up test files */
    for (i = 0; i < NUM_TEST_FILES; i++) {
        snprintf(filename, sizeof(filename), "%s/test_file_%d", test_dir, i);
        unlink(filename);
    }
    
    free(data);
    return ret;
}

/* Test read-heavy data detection */
int test_read_heavy_detection(const char *test_dir) {
    char filename[256];
    char *data;
    int fd, i;
    int ret = 0;
    
    printf("Testing read-heavy data detection...\n");
    
    data = malloc(TEST_FILE_SIZE);
    if (!data) {
        printf("Error: Cannot allocate test data\n");
        return -1;
    }
    
    snprintf(filename, sizeof(filename), "%s/read_heavy_test", test_dir);
    
    /* Create a test file */
    fd = open(filename, O_CREAT | O_RDWR | O_TRUNC, 0644);
    if (fd < 0) {
        printf("Error: Cannot create %s: %s\n", filename, strerror(errno));
        free(data);
        return -1;
    }
    
    /* Write test data */
    memset(data, 0xBB, TEST_FILE_SIZE);
    if (write(fd, data, TEST_FILE_SIZE) != TEST_FILE_SIZE) {
        printf("Error: Cannot write to %s: %s\n", filename, strerror(errno));
        close(fd);
        unlink(filename);
        free(data);
        return -1;
    }
    
    /* Perform multiple reads to trigger read-heavy detection */
    for (i = 0; i < 100; i++) {
        lseek(fd, 0, SEEK_SET);
        if (read(fd, data, TEST_FILE_SIZE) != TEST_FILE_SIZE) {
            printf("Error: Cannot read from %s: %s\n", filename, strerror(errno));
            ret = -1;
            break;
        }
        
        if (i % 10 == 0) {
            printf("Performed %d reads\n", i + 1);
        }
    }
    
    close(fd);
    unlink(filename);
    free(data);
    
    return ret;
}

int main(int argc, char *argv[]) {
    char *mount_point = NULL;
    char *test_dir = NULL;
    int ret = 0;
    
    if (argc != 3) {
        printf("Usage: %s <f2fs_mount_point> <test_directory>\n", argv[0]);
        printf("Example: %s sda1 /mnt/f2fs/test\n", argv[0]);
        return 1;
    }
    
    mount_point = argv[1];
    test_dir = argv[2];
    
    printf("F2FS Wear Leveling Test Program\n");
    printf("Mount point: %s\n", mount_point);
    printf("Test directory: %s\n", test_dir);
    printf("================================\n\n");
    
    /* Test sysfs interface */
    if (test_sysfs_interface(mount_point) != 0) {
        printf("Warning: Some sysfs tests failed\n");
    }
    printf("\n");
    
    /* Test CV allocation */
    if (test_cv_allocation(test_dir) != 0) {
        printf("Error: CV allocation test failed\n");
        ret = 1;
    }
    printf("\n");
    
    /* Test read-heavy detection */
    if (test_read_heavy_detection(test_dir) != 0) {
        printf("Error: Read-heavy detection test failed\n");
        ret = 1;
    }
    printf("\n");
    
    printf("Test completed with %s\n", ret == 0 ? "SUCCESS" : "ERRORS");
    return ret;
}
